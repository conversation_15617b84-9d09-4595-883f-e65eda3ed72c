<template>
  <div class="layout-container">
    <!-- 顶部导航 -->
    <div class="layout-header">
      <div class="header-left">
        <div class="logo-section">
          <div class="logo-icon">
            <img src="@/assets/logo.png" alt="Logo" class="logo-image" />
          </div>
          <h1 class="system-title">智索管理平台</h1>
        </div>
      </div>

      <div class="header-center">
        <div class="search-box">
          <el-input
            placeholder="搜索..."
            prefix-icon="Search"
            v-model="searchKeyword"
            style="width: 300px;"
          />
        </div>
      </div>

      <div class="header-right">
        <div class="header-actions">
          <el-button type="text" class="action-btn">
            <el-icon size="18"><Bell /></el-icon>
          </el-button>
          <el-button type="text" class="action-btn">
            <el-icon size="18"><QuestionFilled /></el-icon>
          </el-button>
        </div>

        <el-dropdown @command="handleCommand">
          <div class="user-info">
            <el-avatar :size="32" :src="userInfo?.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
            <span class="username">{{ userInfo?.realName || '管理员' }}</span>
            <el-icon class="arrow-down"><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人信息
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>
                系统设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <!-- 主体内容 -->
    <div class="layout-content">
      <!-- 侧边栏 -->
      <div class="layout-sidebar" :class="{ collapsed: isCollapsed }">
        <div class="sidebar-header">
          <el-button
            class="collapse-btn"
            @click="toggleSidebar"
            :icon="isCollapsed ? Expand : Fold"
            circle
            size="default"
          />
        </div>

        <el-menu
          :default-active="activeMenu"
          :unique-opened="true"
          router
          class="sidebar-menu"
          background-color="#FFFFFF"
          text-color="#6B7280"
          active-text-color="#FFFFFF"
          :collapse="isCollapsed"
        >
          <el-menu-item index="/dashboard" class="menu-item">
            <el-icon><DataBoard /></el-icon>
            <template #title>数据概览</template>
          </el-menu-item>

          <el-menu-item index="/users" class="menu-item">
            <el-icon><User /></el-icon>
            <template #title>用户管理</template>
          </el-menu-item>

          <el-menu-item index="/articles" class="menu-item">
            <el-icon><Document /></el-icon>
            <template #title>内容管理</template>
          </el-menu-item>

          <el-menu-item index="/topics" class="menu-item">
            <el-icon><ChatDotRound /></el-icon>
            <template #title>热点话题</template>
          </el-menu-item>

          <el-menu-item index="/statistics" class="menu-item">
            <el-icon><TrendCharts /></el-icon>
            <template #title>数据统计</template>
          </el-menu-item>

          <el-menu-item index="/system" class="menu-item">
            <el-icon><Setting /></el-icon>
            <template #title>系统管理</template>
          </el-menu-item>
        </el-menu>
      </div>
      
      <!-- 主要内容区域 -->
      <div class="layout-main">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { Expand, Fold } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const isCollapsed = ref(false)
const searchKeyword = ref('')

// 计算当前激活的菜单
const activeMenu = computed(() => {
  return route.path
})

// 用户信息
const userInfo = computed(() => authStore.userInfo)

// 切换侧边栏
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

// 处理下拉菜单命令
const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人信息功能开发中...')
      break
    case 'settings':
      router.push('/system')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await authStore.logout()
        ElMessage.success('退出登录成功')
        router.push('/login')
      } catch (error) {
        // 用户取消操作
      }
      break
  }
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.layout-header {
  height: 64px;
  background: #2D3748;
  border-bottom: 1px solid #4A5568;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 1000;

  .header-left {
    .logo-section {
      display: flex;
      align-items: center;
      gap: 12px;

      .logo-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;

        .logo-image {
          width: 32px;
          height: 32px;
          object-fit: contain;
          border-radius: 6px;
        }
      }

      .system-title {
        font-size: 20px;
        font-weight: 700;
        color: #FFFFFF;
        margin: 0;
      }
    }
  }

  .header-center {
    flex: 1;
    display: flex;
    justify-content: center;

    .search-box {
      :deep(.el-input) {
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #718096;
          box-shadow: none;
          background: #4A5568;

          &:hover {
            border-color: #8B5CF6;
          }

          &.is-focus {
            border-color: #8B5CF6;
            box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
          }
        }

        .el-input__inner {
          color: #FFFFFF;

          &::placeholder {
            color: #A0AEC0;
          }
        }

        .el-input__prefix-inner {
          color: #A0AEC0;
        }
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;

    .header-actions {
      display: flex;
      gap: 8px;

      .action-btn {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #A0AEC0;

        &:hover {
          background: #4A5568;
          color: #FFFFFF;
        }
      }
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 16px;
      border-radius: 8px;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background: #4A5568;
      }

      .username {
        font-size: 14px;
        font-weight: 500;
        color: #FFFFFF;
      }

      .arrow-down {
        font-size: 12px;
        color: #A0AEC0;
      }
    }
  }
}

.layout-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-sidebar {
  width: 200px;
  background: #FFFFFF;
  transition: width 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 100;
  overflow: hidden;
  border-right: 1px solid #E5E7EB;

  &.collapsed {
    width: 64px;

    .sidebar-header {
      padding: 16px 6px;
      justify-content: center;
    }

    .sidebar-menu {
      :deep(.el-menu-item) {
        padding: 0 !important;
        margin: 4px 8px !important;
        justify-content: center !important;
        width: 48px !important;
        min-width: 48px !important;
        border-radius: 8px !important;

        .el-icon {
          margin-right: 0 !important;
        }

        span {
          opacity: 0 !important;
          width: 0 !important;
          overflow: hidden !important;
          transition: none !important;
        }
      }

      :deep(.el-tooltip__trigger) {
        width: 100% !important;
        display: flex !important;
        justify-content: center !important;
      }
    }
  }

  .sidebar-header {
    padding: 16px 12px;
    border-bottom: 1px solid #E5E7EB;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-height: 64px;

    .collapse-btn {
      background: #F3F4F6;
      border: none;
      color: #6B7280;
      transition: all 0.3s ease;
      width: 36px;
      height: 36px;
      font-size: 16px;

      &:hover {
        background: #8B5CF6;
        color: #FFFFFF;
        transform: scale(1.05);
      }

      :deep(.el-icon) {
        font-size: 16px;
      }
    }
  }

  .sidebar-menu {
    border: none;
    height: calc(100vh - 64px);
    background: #FFFFFF;
    overflow: hidden;

    :deep(.el-menu-item) {
      height: 48px;
      line-height: 48px;
      margin: 4px 8px;
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      align-items: center;
      white-space: nowrap;
      overflow: hidden;

      &:hover {
        background-color: #F3F4F6 !important;
        color: #374151 !important;
      }

      &.is-active {
        background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%) !important;
        color: #FFFFFF !important;

        .el-icon {
          color: #FFFFFF !important;
        }
      }

      .el-icon {
        color: #6B7280;
        transition: color 0.3s ease, margin-right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        margin-right: 12px;
        font-size: 18px;
        flex-shrink: 0;
      }

      span {
        font-weight: 500;
        transition: opacity 0.2s ease 0.1s, width 0.25s cubic-bezier(0.4, 0, 0.2, 1) 0s;
        white-space: nowrap;
        overflow: hidden;
        opacity: 1;
        width: auto;
        flex-shrink: 0;
      }
    }

    :deep(.el-sub-menu__title) {
      height: 48px;
      line-height: 48px;
      margin: 4px 8px;
      border-radius: 8px;

      &:hover {
        background-color: #F3F4F6;
        color: #374151;
      }
    }
  }
}

.layout-main {
  flex: 1;
  background: #F9FAFB;
  overflow-y: auto;
  position: relative;
  z-index: 1;
}

@media (max-width: 768px) {
  .layout-sidebar {
    position: fixed;
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    z-index: 999;
    transform: translateX(-100%);
    transition: transform 0.3s;
    
    &:not(.collapsed) {
      transform: translateX(0);
    }
  }
  
  .layout-main {
    padding: 10px;
  }
  
  .header-left .system-title {
    display: none;
  }
}
</style>
