<template>
  <div class="dashboard">
    <div class="page-header">
      <div class="header-left">
        <h2>数据概览</h2>
        <p>欢迎回来，以下是今日数据概览</p>
      </div>
      <div class="header-right">
        <span class="date-info">今日：2023年7月18日</span>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="24" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card">
          <div class="stat-content">
            <div class="stat-title">总用户数</div>
            <div class="stat-value">2</div>
            <div class="stat-trend">
              <span class="trend-text">同比增长</span>
              <span class="trend-value positive">+5.2%</span>
            </div>
          </div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card">
          <div class="stat-content">
            <div class="stat-title">文章总数</div>
            <div class="stat-value">2,345</div>
            <div class="stat-trend">
              <span class="trend-text">同比增长</span>
              <span class="trend-value positive">+8.6%</span>
            </div>
          </div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card">
          <div class="stat-content">
            <div class="stat-title">热点话题</div>
            <div class="stat-value">456</div>
            <div class="stat-trend">
              <span class="trend-text">同比增长</span>
              <span class="trend-value positive">+12.3%</span>
            </div>
          </div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card">
          <div class="stat-content">
            <div class="stat-title">总访问量</div>
            <div class="stat-value">12.3w</div>
            <div class="stat-trend">
              <span class="trend-text">同比增长</span>
              <span class="trend-value positive">+15.1%</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="24" class="charts-row">
      <el-col :xs="24" :lg="16">
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户增长趋势 (30天)</h3>
            <div class="chart-legend">
              <span class="legend-item">
                <span class="legend-color" style="background: #8B5CF6;"></span>
                新增用户
              </span>
            </div>
          </div>
          <div class="chart-container" ref="userChartRef"></div>
        </div>
      </el-col>

      <el-col :xs="24" :lg="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>内容分布</h3>
          </div>
          <div class="chart-container pie-chart" ref="contentChartRef"></div>
        </div>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <div class="table-card">
      <div class="table-header">
        <div class="table-tabs">
          <span class="tab-item active">最新文章</span>
          <span class="tab-item">用户反馈</span>
          <span class="tab-item">数据统计</span>
        </div>
      </div>

      <el-table
        :data="recentArticles"
        style="width: 100%"
        class="data-table"
      >
        <el-table-column prop="id" label="文章ID" width="140" />
        <el-table-column prop="title" label="标题" show-overflow-tooltip />
        <el-table-column prop="views" label="阅读量" width="100" />
        <el-table-column prop="publishTime" label="发布时间" width="160" />
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === '已发布' ? 'success' : 'warning'" size="small">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default>
            <el-button type="primary" link size="small">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { statisticsApi } from '@/api/statistics'

const userChartRef = ref()
const contentChartRef = ref()
const loading = ref(false)

// 统计数据
const stats = ref({
  totalUsers: 12846,
  todayNewUsers: 56,
  totalArticles: 3254,
  todayArticles: 23,
  totalTopics: 298,
  todayTopics: 12,
  totalViews: 1257543,
  todayViews: 3456
})

// 最新文章数据
const recentArticles = ref([
  {
    id: 'A-202307180001',
    title: '如何提升用户体验：10个实用技巧',
    views: 2399,
    publishTime: '2023-07-18 09:23',
    status: '已发布'
  },
  {
    id: 'A-202307180002',
    title: '内容生态建设的5个核心要素',
    views: 1899,
    publishTime: '2023-07-18 10:45',
    status: '草稿'
  },
  {
    id: 'A-202307170023',
    title: '最新行业趋势分析：2023年下半年展望',
    views: 3288,
    publishTime: '2023-07-17 16:20',
    status: '已发布'
  },
  {
    id: 'A-202307170018',
    title: '提高团队协作效率的工具',
    views: 1543,
    publishTime: '2023-07-17 14:30',
    status: '已发布'
  },
  {
    id: 'A-202307170012',
    title: '数据驱动决策：从理论到实践',
    views: 2176,
    publishTime: '2023-07-17 11:05',
    status: '草稿'
  }
])



// 获取统计数据
const fetchStatistics = async () => {
  loading.value = true
  try {
    const response = await statisticsApi.getOverallStatistics()
    if (response.code === 200) {
      stats.value = response.data
    } else {
      console.error('获取统计数据失败:', response.message)
      // 使用默认数据
      stats.value = {
        totalUsers: 1234,
        todayNewUsers: 56,
        totalArticles: 2345,
        todayArticles: 23,
        totalTopics: 456,
        todayTopics: 12,
        totalViews: 123456,
        todayViews: 3456
      }
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 使用默认数据
    stats.value = {
      totalUsers: 1234,
      todayNewUsers: 56,
      totalArticles: 2345,
      todayArticles: 23,
      totalTopics: 456,
      todayTopics: 12,
      totalViews: 123456,
      todayViews: 3456
    }
  } finally {
    loading.value = false
  }
}

// 初始化用户增长图表
const initUserChart = () => {
  const chart = echarts.init(userChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#E5E7EB',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      axisLine: {
        lineStyle: {
          color: '#E5E7EB'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#6B7280'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#6B7280'
      },
      splitLine: {
        lineStyle: {
          color: '#F3F4F6'
        }
      }
    },
    series: [{
      data: [3200, 2800, 3600, 4200, 5000, 5400, 5800, 6200, 6000, 6400, 6800, 7200],
      type: 'bar',
      barWidth: '50%',
      itemStyle: {
        color: '#8B5CF6',
        borderRadius: [6, 6, 0, 0]
      },
      emphasis: {
        itemStyle: {
          color: '#7C3AED'
        }
      }
    }]
  }
  chart.setOption(option)
  
  // 响应式
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 初始化内容分布图表
const initContentChart = () => {
  const chart = echarts.init(contentChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#E5E7EB',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      left: 'center',
      textStyle: {
        color: '#6B7280',
        fontSize: 12
      },
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 16
    },
    series: [{
      name: '内容分布',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '45%'],
      data: [
        { value: 35, name: '科技', itemStyle: { color: '#8B5CF6' } },
        { value: 25, name: '财经', itemStyle: { color: '#10B981' } },
        { value: 20, name: '社会', itemStyle: { color: '#3B82F6' } },
        { value: 10, name: '娱乐', itemStyle: { color: '#F59E0B' } },
        { value: 10, name: '体育', itemStyle: { color: '#EF4444' } }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        show: true,
        position: 'inside',
        formatter: '{d}%',
        fontSize: 12,
        color: '#FFFFFF',
        fontWeight: 'bold'
      },
      labelLine: {
        show: false
      }
    }]
  }
  chart.setOption(option)

  // 响应式
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

onMounted(async () => {
  // 获取统计数据
  await fetchStatistics()

  await nextTick()
  initUserChart()
  initContentChart()
})
</script>

<style lang="scss" scoped>
.dashboard {
  background: #F8FAFC;
  min-height: 100vh;
  padding: 24px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;

    .header-left {
      h2 {
        font-size: 28px;
        font-weight: 700;
        color: #1F2937;
        margin: 0 0 8px 0;
      }

      p {
        color: #6B7280;
        margin: 0;
        font-size: 16px;
      }
    }

    .header-right {
      .date-info {
        color: #6B7280;
        font-size: 14px;
        background: #FFFFFF;
        padding: 8px 16px;
        border-radius: 8px;
        border: 1px solid #E5E7EB;
      }
    }
  }

  .stats-row {
    margin-bottom: 32px;
  }

  .stat-card {
    background: #FFFFFF;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #F3F4F6;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .stat-content {
      .stat-title {
        font-size: 14px;
        color: #6B7280;
        margin-bottom: 12px;
        font-weight: 500;
      }

      .stat-value {
        font-size: 36px;
        font-weight: 700;
        color: #1F2937;
        margin-bottom: 12px;
        line-height: 1;
      }

      .stat-trend {
        display: flex;
        align-items: center;
        gap: 8px;

        .trend-text {
          font-size: 12px;
          color: #9CA3AF;
        }

        .trend-value {
          font-size: 12px;
          font-weight: 600;

          &.positive {
            color: #10B981;
          }

          &.negative {
            color: #EF4444;
          }
        }
      }
    }
  }

  .charts-row {
    margin-bottom: 32px;
  }

  .chart-card {
    background: #FFFFFF;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #F3F4F6;
    height: 400px;

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      h3 {
        font-size: 18px;
        font-weight: 600;
        color: #1F2937;
        margin: 0;
      }

      .chart-legend {
        display: flex;
        gap: 16px;

        .legend-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          color: #6B7280;

          .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
          }
        }
      }
    }

    .chart-container {
      height: calc(100% - 60px);

      &.pie-chart {
        height: calc(100% - 60px);
        min-height: 300px;
      }
    }
  }

  .table-card {
    background: #FFFFFF;
    border-radius: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #F3F4F6;
    overflow: hidden;

    .table-header {
      padding: 24px 24px 0;

      .table-tabs {
        display: flex;
        gap: 32px;
        border-bottom: 1px solid #F3F4F6;

        .tab-item {
          padding: 12px 0;
          font-size: 14px;
          font-weight: 500;
          color: #6B7280;
          cursor: pointer;
          position: relative;
          transition: color 0.3s ease;

          &.active {
            color: #8B5CF6;

            &::after {
              content: '';
              position: absolute;
              bottom: -1px;
              left: 0;
              right: 0;
              height: 2px;
              background: #8B5CF6;
              border-radius: 1px;
            }
          }

          &:hover:not(.active) {
            color: #374151;
          }
        }
      }
    }

    .data-table {
      .el-table__header {
        background: #F9FAFB;

        th {
          background: #F9FAFB !important;
          color: #6B7280;
          font-weight: 600;
          font-size: 14px;
          border-bottom: 1px solid #F3F4F6;
        }
      }

      .el-table__body {
        tr {
          &:hover {
            background: #F9FAFB;
          }

          td {
            border-bottom: 1px solid #F3F4F6;
            color: #374151;
            font-size: 14px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-left h2 {
        font-size: 24px;
      }
    }

    .stat-card {
      padding: 20px;

      .stat-content .stat-value {
        font-size: 28px;
      }

      .stat-icon {
        width: 40px;
        height: 40px;
      }
    }

    .chart-card {
      padding: 20px;
      height: 350px;

      .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .chart-legend {
          flex-wrap: wrap;
        }
      }

      .chart-container {
        &.pie-chart {
          height: calc(100% - 80px);
          min-height: 250px;
        }
      }
    }

    .table-card {
      .table-header {
        padding: 20px 20px 0;

        .table-tabs {
          gap: 24px;

          .tab-item {
            font-size: 13px;
          }
        }
      }
    }
  }
}
</style>
